{"$schema": "https://json.schemastore.org/swcrc", "jsc": {"parser": {"syntax": "typescript", "tsx": false, "decorators": true, "dynamicImport": true}, "transform": {"legacyDecorator": true, "decoratorMetadata": true}, "target": "es2023", "loose": false, "externalHelpers": false, "keepClassNames": true, "preserveAllComments": false}, "module": {"type": "commonjs", "strict": false, "strictMode": true, "lazy": false, "noInterop": false}, "minify": true, "isModule": true, "sourceMaps": false, "inlineSourcesContent": false}